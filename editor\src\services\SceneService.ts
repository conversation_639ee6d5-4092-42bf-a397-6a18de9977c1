/**
 * 场景服务
 * 负责场景的加载、保存和管理
 */
import axios from 'axios';
import EngineService, { EngineEventType } from './EngineService';
import { EventEmitter } from '../libs/dl-engine';

// 使用 any 类型避免复杂的类型推导和冲突
type EngineScene = any;
type EngineEntity = any;

// 场景事件类型
export enum SceneEventType {
  LOADING_START = 'loadingStart',
  LOADING_PROGRESS = 'loadingProgress',
  LOADING_COMPLETE = 'loadingComplete',
  LOADING_ERROR = 'loadingError',
  SAVING_START = 'savingStart',
  SAVING_PROGRESS = 'savingProgress',
  SAVING_COMPLETE = 'savingComplete',
  SAVING_ERROR = 'savingError',
  SCENE_CHANGED = 'sceneChanged',
  SCENE_GRAPH_CHANGED = 'sceneGraphChanged',
  SCENE_CREATED = 'sceneCreated',
  SCENE_DELETED = 'sceneDeleted',
  SCENE_RENAMED = 'sceneRenamed',
  SCENE_COPIED = 'sceneCopied',
  SCENE_EXPORTED = 'sceneExported',
  SCENE_IMPORTED = 'sceneImported',
  NODE_SELECTED = 'nodeSelected',
  NODE_DESELECTED = 'nodeDeselected',
  VALIDATION_ERROR = 'validationError',
  UNDO_PERFORMED = 'undoPerformed',
  REDO_PERFORMED = 'redoPerformed',
}

// 场景图节点
export interface SceneGraphNode {
  id: string;
  name: string;
  type: string;
  children: SceneGraphNode[];
  components: string[];
  visible: boolean;
  locked: boolean;
  selected?: boolean;
  expanded?: boolean;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  scale?: { x: number; y: number; z: number };
}

// 场景信息
export interface SceneInfo {
  id: string;
  name: string;
  description?: string;
  thumbnail?: string;
  createdAt: Date;
  updatedAt: Date;
  version: number;
  template: string;
  tags: string[];
  metadata: Record<string, any>;
}

// 场景模板
export interface SceneTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  data: any;
  category: string;
  isBuiltIn: boolean;
}

// 撤销/重做操作
export interface UndoRedoOperation {
  id: string;
  type: string;
  description: string;
  timestamp: Date;
  data: any;
  inverse: any;
}

// 场景验证结果
export interface SceneValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// 导出选项
export interface SceneExportOptions {
  format: 'json' | 'gltf' | 'fbx' | 'obj';
  includeAssets: boolean;
  compressAssets: boolean;
  optimizeForWeb: boolean;
  exportPath?: string;
}

// 场景服务类
export class SceneService extends EventEmitter {
  private static instance: SceneService;

  private currentSceneId: string | null = null;
  private currentProjectId: string | null = null;
  private sceneGraph: SceneGraphNode | null = null;
  private isDirty: boolean = false;
  private autoSaveInterval: number | null = null;

  // 新增属性
  private selectedNodes: Set<string> = new Set();
  private undoStack: UndoRedoOperation[] = [];
  private redoStack: UndoRedoOperation[] = [];
  private maxUndoStackSize: number = 50;
  private sceneTemplates: Map<string, SceneTemplate> = new Map();
  private sceneInfoCache: Map<string, SceneInfo> = new Map();
  private validationErrors: string[] = [];
  private lastValidationTime: Date | null = null;
  
  private constructor() {
    super();

    // 监听引擎事件
    EngineService.addEventListener(EngineEventType.SCENE_LOADED, this.handleSceneLoaded.bind(this));
    EngineService.addEventListener(EngineEventType.SCENE_UNLOADED, this.handleSceneUnloaded.bind(this));
    EngineService.addEventListener(EngineEventType.OBJECT_ADDED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.OBJECT_REMOVED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.OBJECT_CHANGED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.COMPONENT_ADDED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.COMPONENT_REMOVED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.COMPONENT_CHANGED, this.handleObjectChanged.bind(this));
  }
  
  /**
   * 获取场景服务实例
   */
  public static getInstance(): SceneService {
    if (!SceneService.instance) {
      SceneService.instance = new SceneService();
    }
    return SceneService.instance;
  }
  
  /**
   * 加载场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  public async loadScene(projectId: string, sceneId: string): Promise<EngineScene> {
    try {
      // 发出加载开始事件
      this.emit(SceneEventType.LOADING_START, { projectId, sceneId });
      
      // 从API获取场景数据
      const response = await axios.get(`/api/projects/${projectId}/scenes/${sceneId}/data`);
      const sceneData = response.data;

      // 使用自定义方法加载场景数据
      await this.loadSceneFromData(sceneData);

      // 加载场景到引擎
      const scene = await EngineService.loadScene(sceneData);
      
      // 设置当前场景和项目ID
      this.currentSceneId = sceneId;
      this.currentProjectId = projectId;
      this.isDirty = false;
      
      // 更新场景图
      this.updateSceneGraph();
      
      // 发出加载完成事件
      this.emit(SceneEventType.LOADING_COMPLETE, { scene, projectId, sceneId });
      
      // 设置自动保存
      this.setupAutoSave();
      
      return scene;
    } catch (error) {
      console.error('加载场景失败:', error);
      this.emit(SceneEventType.LOADING_ERROR, { error, projectId, sceneId });
      throw error;
    }
  }
  
  /**
   * 保存场景
   */
  public async saveScene(): Promise<void> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }
    
    try {
      // 发出保存开始事件
      this.emit(SceneEventType.SAVING_START, { projectId: this.currentProjectId, sceneId: this.currentSceneId });

      // 使用自定义方法序列化场景
      const customSceneData = await this.serializeScene();

      // 同时获取引擎的场景数据
      const engineSceneData = await EngineService.saveScene();

      // 合并数据
      const sceneData = {
        ...engineSceneData,
        customData: customSceneData
      };
      
      // 保存到API
      await axios.put(`/api/projects/${this.currentProjectId}/scenes/${this.currentSceneId}/data`, sceneData);
      
      // 更新状态
      this.isDirty = false;
      
      // 发出保存完成事件
      this.emit(SceneEventType.SAVING_COMPLETE, { projectId: this.currentProjectId, sceneId: this.currentSceneId });
    } catch (error) {
      console.error('保存场景失败:', error);
      this.emit(SceneEventType.SAVING_ERROR, { error, projectId: this.currentProjectId, sceneId: this.currentSceneId });
      throw error;
    }
  }
  
  /**
   * 创建新场景
   * @param projectId 项目ID
   * @param name 场景名称
   * @param template 场景模板
   */
  public async createScene(projectId: string, name: string, template: string = 'empty'): Promise<any> {
    try {
      // 创建场景
      const response = await axios.post(`/api/projects/${projectId}/scenes`, {
        name,
        template,
      });
      
      const sceneData = response.data;
      
      // 加载新场景
      await this.loadScene(projectId, sceneData.id);
      
      return sceneData;
    } catch (error) {
      console.error('创建场景失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新场景图
   */
  public updateSceneGraph(): void {
    const scene = EngineService.getActiveScene();
    if (!scene) {
      this.sceneGraph = null;
      return;
    }

    // 构建场景图 - 获取所有实体
    const entities = scene.getEntities();
    if (entities.length > 0) {
      // 假设第一个实体是根实体，或者创建一个虚拟根节点
      this.sceneGraph = this.buildSceneGraphFromEntities(entities);
    } else {
      this.sceneGraph = null;
    }

    // 发出场景图变化事件
    this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
  }
  
  /**
   * 从实体列表构建场景图
   * @param entities 实体列表
   */
  private buildSceneGraphFromEntities(entities: EngineEntity[]): SceneGraphNode {
    // 创建虚拟根节点
    const rootNode: SceneGraphNode = {
      id: 'root',
      name: 'Scene Root',
      type: 'Root',
      children: [],
      components: [],
      visible: true,
      locked: false,
    };

    // 将所有实体作为根节点的子节点
    for (const entity of entities) {
      rootNode.children.push(this.buildSceneGraphNode(entity));
    }

    return rootNode;
  }

  /**
   * 构建场景图节点
   * @param entity 实体
   */
  private buildSceneGraphNode(entity: EngineEntity): SceneGraphNode {
    // 由于 dl-engine.d.ts 中的 Entity 类方法有限，我们使用基本属性
    const node: SceneGraphNode = {
      id: entity.id || 'unknown',
      name: entity.name || 'Entity',
      type: 'Entity',
      children: [],
      components: [], // 暂时为空，因为 getAllComponents 方法不存在
      visible: true, // 默认可见
      locked: false, // 默认未锁定
    };

    return node;
  }
  
  /**
   * 获取场景图
   */
  public getSceneGraph(): SceneGraphNode | null {
    return this.sceneGraph;
  }
  
  /**
   * 获取当前场景ID
   */
  public getCurrentSceneId(): string | null {
    return this.currentSceneId;
  }
  
  /**
   * 获取当前项目ID
   */
  public getCurrentProjectId(): string | null {
    return this.currentProjectId;
  }
  
  /**
   * 检查场景是否有未保存的更改
   */
  public isDirtyScene(): boolean {
    return this.isDirty;
  }
  
  /**
   * 设置自动保存
   * @param interval 自动保存间隔（毫秒），如果为0则禁用自动保存
   */
  public setupAutoSave(interval: number = 300000): void {
    // 清除现有的自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    // 如果间隔大于0，设置新的自动保存
    if (interval > 0) {
      this.autoSaveInterval = window.setInterval(async () => {
        if (this.isDirty) {
          try {
            await this.saveScene();
            console.log('自动保存成功');
          } catch (error) {
            console.error('自动保存失败:', error);
          }
        }
      }, interval);
    }
  }
  
  /**
   * 处理场景加载事件
   */
  private handleSceneLoaded(scene: EngineScene): void {
    this.updateSceneGraph();
    this.isDirty = false;
    this.emit(SceneEventType.SCENE_CHANGED, scene);
  }

  /**
   * 处理场景卸载事件
   */
  private handleSceneUnloaded(_scene: EngineScene): void {
    this.sceneGraph = null;
    this.isDirty = false;
    this.emit(SceneEventType.SCENE_CHANGED, null);
  }
  
  /**
   * 处理对象变化事件
   */
  private handleObjectChanged(): void {
    this.updateSceneGraph();
    this.isDirty = true;
    this.emit(SceneEventType.SCENE_CHANGED, EngineService.getActiveScene());
  }

  // ==================== 场景管理功能 ====================

  /**
   * 删除场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  public async deleteScene(projectId: string, sceneId: string): Promise<void> {
    try {
      // 如果删除的是当前场景，先卸载
      if (this.currentSceneId === sceneId) {
        await this.unloadCurrentScene();
      }

      // 从API删除场景
      await axios.delete(`/api/projects/${projectId}/scenes/${sceneId}`);

      // 从缓存中移除
      this.sceneInfoCache.delete(sceneId);

      // 发出删除事件
      this.emit(SceneEventType.SCENE_DELETED, { projectId, sceneId });

      console.log('场景删除成功');
    } catch (error) {
      console.error('删除场景失败:', error);
      throw error;
    }
  }

  /**
   * 重命名场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param newName 新名称
   */
  public async renameScene(projectId: string, sceneId: string, newName: string): Promise<void> {
    try {
      // 更新场景名称
      await axios.patch(`/api/projects/${projectId}/scenes/${sceneId}`, {
        name: newName
      });

      // 更新缓存
      const sceneInfo = this.sceneInfoCache.get(sceneId);
      if (sceneInfo) {
        sceneInfo.name = newName;
        sceneInfo.updatedAt = new Date();
      }

      // 发出重命名事件
      this.emit(SceneEventType.SCENE_RENAMED, { projectId, sceneId, newName });

      console.log('场景重命名成功');
    } catch (error) {
      console.error('重命名场景失败:', error);
      throw error;
    }
  }

  /**
   * 复制场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param newName 新场景名称
   */
  public async copyScene(projectId: string, sceneId: string, newName: string): Promise<any> {
    try {
      // 复制场景
      const response = await axios.post(`/api/projects/${projectId}/scenes/${sceneId}/copy`, {
        name: newName
      });

      const newSceneData = response.data;

      // 发出复制事件
      this.emit(SceneEventType.SCENE_COPIED, {
        projectId,
        originalSceneId: sceneId,
        newSceneId: newSceneData.id,
        newName
      });

      console.log('场景复制成功');
      return newSceneData;
    } catch (error) {
      console.error('复制场景失败:', error);
      throw error;
    }
  }

  /**
   * 获取项目中的所有场景
   * @param projectId 项目ID
   */
  public async getScenes(projectId: string): Promise<SceneInfo[]> {
    try {
      const response = await axios.get(`/api/projects/${projectId}/scenes`);
      const scenes = response.data.map((scene: any) => ({
        ...scene,
        createdAt: new Date(scene.createdAt),
        updatedAt: new Date(scene.updatedAt)
      }));

      // 更新缓存
      scenes.forEach((scene: SceneInfo) => {
        this.sceneInfoCache.set(scene.id, scene);
      });

      return scenes;
    } catch (error) {
      console.error('获取场景列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取场景信息
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  public async getSceneInfo(projectId: string, sceneId: string): Promise<SceneInfo> {
    try {
      // 先检查缓存
      const cached = this.sceneInfoCache.get(sceneId);
      if (cached) {
        return cached;
      }

      // 从API获取
      const response = await axios.get(`/api/projects/${projectId}/scenes/${sceneId}/info`);
      const sceneInfo: SceneInfo = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      };

      // 更新缓存
      this.sceneInfoCache.set(sceneId, sceneInfo);

      return sceneInfo;
    } catch (error) {
      console.error('获取场景信息失败:', error);
      throw error;
    }
  }

  /**
   * 卸载当前场景
   */
  public async unloadCurrentScene(): Promise<void> {
    if (!this.currentSceneId) {
      return;
    }

    try {
      // 如果有未保存的更改，提示用户
      if (this.isDirty) {
        console.warn('场景有未保存的更改');
      }

      // 卸载场景
      await EngineService.unloadScene();

      // 清理状态
      this.currentSceneId = null;
      this.currentProjectId = null;
      this.sceneGraph = null;
      this.isDirty = false;
      this.selectedNodes.clear();
      this.clearUndoRedoStack();

      console.log('当前场景已卸载');
    } catch (error) {
      console.error('卸载场景失败:', error);
      throw error;
    }
  }

  // ==================== 场景图节点操作 ====================

  /**
   * 选择节点
   * @param nodeId 节点ID
   * @param multiSelect 是否多选
   */
  public selectNode(nodeId: string, multiSelect: boolean = false): void {
    if (!multiSelect) {
      this.selectedNodes.clear();
    }

    this.selectedNodes.add(nodeId);
    this.updateNodeSelection(nodeId, true);
    this.emit(SceneEventType.NODE_SELECTED, { nodeId, selectedNodes: Array.from(this.selectedNodes) });
  }

  /**
   * 取消选择节点
   * @param nodeId 节点ID
   */
  public deselectNode(nodeId: string): void {
    this.selectedNodes.delete(nodeId);
    this.updateNodeSelection(nodeId, false);
    this.emit(SceneEventType.NODE_DESELECTED, { nodeId, selectedNodes: Array.from(this.selectedNodes) });
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    const selectedNodes = Array.from(this.selectedNodes);
    this.selectedNodes.clear();

    // 更新场景图中的选择状态
    selectedNodes.forEach(nodeId => {
      this.updateNodeSelection(nodeId, false);
    });

    this.emit(SceneEventType.NODE_DESELECTED, { nodeId: null, selectedNodes: [] });
  }

  /**
   * 获取选中的节点
   */
  public getSelectedNodes(): string[] {
    return Array.from(this.selectedNodes);
  }

  /**
   * 更新节点选择状态
   * @param nodeId 节点ID
   * @param selected 是否选中
   */
  private updateNodeSelection(nodeId: string, selected: boolean): void {
    if (!this.sceneGraph) return;

    const updateNode = (node: SceneGraphNode): boolean => {
      if (node.id === nodeId) {
        node.selected = selected;
        return true;
      }

      for (const child of node.children) {
        if (updateNode(child)) {
          return true;
        }
      }

      return false;
    };

    updateNode(this.sceneGraph);
    this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
  }

  /**
   * 重命名节点
   * @param nodeId 节点ID
   * @param newName 新名称
   */
  public renameNode(nodeId: string, newName: string): void {
    if (!this.sceneGraph) return;

    const renameNodeRecursive = (node: SceneGraphNode): boolean => {
      if (node.id === nodeId) {
        const oldName = node.name;
        node.name = newName;

        // 记录撤销操作
        this.addUndoOperation({
          id: this.generateOperationId(),
          type: 'renameNode',
          description: `重命名节点 "${oldName}" 为 "${newName}"`,
          timestamp: new Date(),
          data: { nodeId, newName },
          inverse: { nodeId, newName: oldName }
        });

        return true;
      }

      for (const child of node.children) {
        if (renameNodeRecursive(child)) {
          return true;
        }
      }

      return false;
    };

    if (renameNodeRecursive(this.sceneGraph)) {
      this.isDirty = true;
      this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
    }
  }

  /**
   * 删除节点
   * @param nodeId 节点ID
   */
  public deleteNode(nodeId: string): void {
    if (!this.sceneGraph) return;

    const deleteNodeRecursive = (parent: SceneGraphNode, index: number): SceneGraphNode | null => {
      const node = parent.children[index];
      if (node.id === nodeId) {
        // 记录撤销操作
        this.addUndoOperation({
          id: this.generateOperationId(),
          type: 'deleteNode',
          description: `删除节点 "${node.name}"`,
          timestamp: new Date(),
          data: { nodeId },
          inverse: { parentId: parent.id, index, nodeData: { ...node } }
        });

        // 从选择中移除
        this.selectedNodes.delete(nodeId);

        // 删除节点
        return parent.children.splice(index, 1)[0];
      }

      for (let i = 0; i < node.children.length; i++) {
        const deleted = deleteNodeRecursive(node, i);
        if (deleted) {
          return deleted;
        }
      }

      return null;
    };

    // 检查根节点
    if (this.sceneGraph.id === nodeId) {
      console.warn('无法删除根节点');
      return;
    }

    for (let i = 0; i < this.sceneGraph.children.length; i++) {
      const deleted = deleteNodeRecursive(this.sceneGraph, i);
      if (deleted) {
        this.isDirty = true;
        this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
        break;
      }
    }
  }

  // ==================== 撤销/重做功能 ====================

  /**
   * 添加撤销操作
   * @param operation 操作
   */
  private addUndoOperation(operation: UndoRedoOperation): void {
    this.undoStack.push(operation);

    // 限制撤销栈大小
    if (this.undoStack.length > this.maxUndoStackSize) {
      this.undoStack.shift();
    }

    // 清空重做栈
    this.redoStack = [];
  }

  /**
   * 撤销操作
   */
  public undo(): boolean {
    if (this.undoStack.length === 0) {
      return false;
    }

    const operation = this.undoStack.pop()!;

    try {
      this.executeOperation(operation.inverse, operation.type);
      this.redoStack.push(operation);
      this.emit(SceneEventType.UNDO_PERFORMED, operation);
      return true;
    } catch (error) {
      console.error('撤销操作失败:', error);
      return false;
    }
  }

  /**
   * 重做操作
   */
  public redo(): boolean {
    if (this.redoStack.length === 0) {
      return false;
    }

    const operation = this.redoStack.pop()!;

    try {
      this.executeOperation(operation.data, operation.type);
      this.undoStack.push(operation);
      this.emit(SceneEventType.REDO_PERFORMED, operation);
      return true;
    } catch (error) {
      console.error('重做操作失败:', error);
      return false;
    }
  }

  /**
   * 执行操作
   * @param data 操作数据
   * @param type 操作类型
   */
  private executeOperation(data: any, type: string): void {
    switch (type) {
      case 'renameNode':
        this.renameNodeDirect(data.nodeId, data.newName);
        break;
      case 'deleteNode':
        // 重新插入节点
        if (data.parentId && data.nodeData) {
          this.insertNodeDirect(data.parentId, data.index, data.nodeData);
        }
        break;
      // 可以添加更多操作类型
      default:
        console.warn('未知的操作类型:', type);
    }
  }

  /**
   * 直接重命名节点（不记录撤销）
   * @param nodeId 节点ID
   * @param newName 新名称
   */
  private renameNodeDirect(nodeId: string, newName: string): void {
    if (!this.sceneGraph) return;

    const renameNodeRecursive = (node: SceneGraphNode): boolean => {
      if (node.id === nodeId) {
        node.name = newName;
        return true;
      }

      for (const child of node.children) {
        if (renameNodeRecursive(child)) {
          return true;
        }
      }

      return false;
    };

    if (renameNodeRecursive(this.sceneGraph)) {
      this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
    }
  }

  /**
   * 直接插入节点（不记录撤销）
   * @param parentId 父节点ID
   * @param index 插入位置
   * @param nodeData 节点数据
   */
  private insertNodeDirect(parentId: string, index: number, nodeData: SceneGraphNode): void {
    if (!this.sceneGraph) return;

    const insertNodeRecursive = (node: SceneGraphNode): boolean => {
      if (node.id === parentId) {
        node.children.splice(index, 0, nodeData);
        return true;
      }

      for (const child of node.children) {
        if (insertNodeRecursive(child)) {
          return true;
        }
      }

      return false;
    };

    if (insertNodeRecursive(this.sceneGraph)) {
      this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
    }
  }

  /**
   * 清空撤销重做栈
   */
  private clearUndoRedoStack(): void {
    this.undoStack = [];
    this.redoStack = [];
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取撤销栈信息
   */
  public getUndoStackInfo(): { canUndo: boolean; canRedo: boolean; undoCount: number; redoCount: number } {
    return {
      canUndo: this.undoStack.length > 0,
      canRedo: this.redoStack.length > 0,
      undoCount: this.undoStack.length,
      redoCount: this.redoStack.length
    };
  }

  // ==================== 场景验证功能 ====================

  /**
   * 验证场景
   * @param scene 场景数据（可选，默认使用当前场景）
   */
  public async validateScene(scene?: any): Promise<SceneValidationResult> {
    const result: SceneValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    try {
      const sceneToValidate = scene || EngineService.getActiveScene();
      if (!sceneToValidate) {
        result.isValid = false;
        result.errors.push('没有可验证的场景');
        return result;
      }

      // 验证场景图结构
      this.validateSceneGraph(result);

      // 验证实体和组件
      this.validateEntities(sceneToValidate, result);

      // 验证资源引用
      await this.validateAssetReferences(sceneToValidate, result);

      // 验证性能相关
      this.validatePerformance(sceneToValidate, result);

      // 更新验证状态
      this.validationErrors = result.errors;
      this.lastValidationTime = new Date();

      if (result.errors.length > 0) {
        result.isValid = false;
        this.emit(SceneEventType.VALIDATION_ERROR, result);
      }

      return result;
    } catch (error) {
      result.isValid = false;
      result.errors.push(`验证过程中发生错误: ${error}`);
      return result;
    }
  }

  /**
   * 验证场景图结构
   * @param result 验证结果
   */
  private validateSceneGraph(result: SceneValidationResult): void {
    if (!this.sceneGraph) {
      result.warnings.push('场景图为空');
      return;
    }

    // 检查重复ID
    const ids = new Set<string>();
    const checkDuplicateIds = (node: SceneGraphNode): void => {
      if (ids.has(node.id)) {
        result.errors.push(`发现重复的节点ID: ${node.id}`);
      } else {
        ids.add(node.id);
      }

      node.children.forEach(checkDuplicateIds);
    };

    checkDuplicateIds(this.sceneGraph);

    // 检查节点名称
    const checkNodeNames = (node: SceneGraphNode): void => {
      if (!node.name || node.name.trim() === '') {
        result.warnings.push(`节点 ${node.id} 没有名称`);
      }

      if (node.name && node.name.length > 100) {
        result.warnings.push(`节点 ${node.id} 名称过长`);
      }

      node.children.forEach(checkNodeNames);
    };

    checkNodeNames(this.sceneGraph);
  }

  /**
   * 验证实体和组件
   * @param scene 场景
   * @param result 验证结果
   */
  private validateEntities(scene: any, result: SceneValidationResult): void {
    try {
      const entities = scene.getEntities ? scene.getEntities() : [];

      if (entities.length === 0) {
        result.warnings.push('场景中没有实体');
        return;
      }

      entities.forEach((entity: any, index: number) => {
        // 检查实体基本属性
        if (!entity.id) {
          result.errors.push(`实体 ${index} 缺少ID`);
        }

        if (!entity.name) {
          result.warnings.push(`实体 ${entity.id || index} 缺少名称`);
        }

        // 检查组件
        if (entity.getComponents) {
          const components = entity.getComponents();
          if (components.size === 0) {
            result.warnings.push(`实体 ${entity.name || entity.id} 没有组件`);
          }
        }
      });

      if (entities.length > 1000) {
        result.warnings.push(`场景中实体数量较多 (${entities.length})，可能影响性能`);
      }
    } catch (error) {
      result.errors.push(`验证实体时发生错误: ${error}`);
    }
  }

  /**
   * 验证资源引用
   * @param scene 场景
   * @param result 验证结果
   */
  private async validateAssetReferences(scene: any, result: SceneValidationResult): Promise<void> {
    try {
      // 检查场景中引用的资源是否存在
      const entities = scene.getEntities ? scene.getEntities() : [];
      let assetCount = 0;

      entities.forEach((entity: any) => {
        // 检查实体的组件中是否有资源引用
        if (entity.getComponents) {
          const components = entity.getComponents();
          components.forEach((component: any) => {
            // 检查常见的资源属性
            if (component.texture || component.material || component.mesh || component.audio) {
              assetCount++;
            }
          });
        }
      });

      if (assetCount > 0) {
        result.suggestions.push(`场景中发现 ${assetCount} 个资源引用，建议定期检查资源的有效性`);
      } else {
        result.suggestions.push('建议定期检查资源引用的有效性');
      }
    } catch (error) {
      result.errors.push(`验证资源引用时发生错误: ${error}`);
    }
  }

  /**
   * 验证性能相关
   * @param scene 场景
   * @param result 验证结果
   */
  private validatePerformance(scene: any, result: SceneValidationResult): void {
    try {
      const entities = scene.getEntities ? scene.getEntities() : [];

      // 检查实体数量
      if (entities.length > 500) {
        result.warnings.push(`实体数量较多 (${entities.length})，建议使用LOD或实例化渲染`);
      }

      // 检查场景图深度
      const getMaxDepth = (node: SceneGraphNode, depth: number = 0): number => {
        if (node.children.length === 0) {
          return depth;
        }
        return Math.max(...node.children.map(child => getMaxDepth(child, depth + 1)));
      };

      if (this.sceneGraph) {
        const maxDepth = getMaxDepth(this.sceneGraph);
        if (maxDepth > 10) {
          result.warnings.push(`场景图层级过深 (${maxDepth})，可能影响性能`);
        }
      }
    } catch (error) {
      result.errors.push(`验证性能时发生错误: ${error}`);
    }
  }

  /**
   * 获取最后验证结果
   */
  public getLastValidationResult(): { errors: string[]; lastValidationTime: Date | null } {
    return {
      errors: [...this.validationErrors],
      lastValidationTime: this.lastValidationTime
    };
  }

  // ==================== 场景导入导出功能 ====================

  /**
   * 导出场景
   * @param options 导出选项
   */
  public async exportScene(options: SceneExportOptions): Promise<Blob> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }

    try {
      // 获取场景数据
      const sceneData = await EngineService.saveScene();

      let exportData: any;

      switch (options.format) {
        case 'json':
          exportData = {
            version: '1.0',
            scene: sceneData,
            metadata: {
              exportedAt: new Date().toISOString(),
              exportOptions: options
            }
          };
          break;

        case 'gltf':
          // 这里需要实现GLTF导出逻辑
          exportData = await this.exportToGLTF(sceneData, options);
          break;

        default:
          throw new Error(`不支持的导出格式: ${options.format}`);
      }

      // 创建Blob
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });

      // 发出导出事件
      this.emit(SceneEventType.SCENE_EXPORTED, {
        projectId: this.currentProjectId,
        sceneId: this.currentSceneId,
        format: options.format,
        size: blob.size
      });

      return blob;
    } catch (error) {
      console.error('导出场景失败:', error);
      throw error;
    }
  }

  /**
   * 导入场景
   * @param file 场景文件
   * @param projectId 项目ID
   */
  public async importScene(file: File, projectId: string): Promise<any> {
    try {
      // 读取文件内容
      const content = await this.readFileContent(file);
      let sceneData: any;

      try {
        sceneData = JSON.parse(content);
      } catch (error) {
        throw new Error('无效的场景文件格式');
      }

      // 验证场景数据
      if (!sceneData.scene) {
        throw new Error('场景文件缺少场景数据');
      }

      // 创建新场景
      const sceneName = file.name.replace(/\.[^/.]+$/, ''); // 移除文件扩展名
      const newScene = await this.createScene(projectId, sceneName, 'imported');

      // 加载场景数据
      await EngineService.loadScene(sceneData.scene);

      // 发出导入事件
      this.emit(SceneEventType.SCENE_IMPORTED, {
        projectId,
        sceneId: newScene.id,
        fileName: file.name,
        fileSize: file.size
      });

      return newScene;
    } catch (error) {
      console.error('导入场景失败:', error);
      throw error;
    }
  }

  /**
   * 读取文件内容
   * @param file 文件
   */
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsText(file);
    });
  }

  /**
   * 导出为GLTF格式
   * @param sceneData 场景数据
   * @param options 导出选项
   */
  private async exportToGLTF(sceneData: any, options: SceneExportOptions): Promise<any> {
    // 这里需要实现GLTF导出逻辑
    // 由于复杂性，这里只是一个占位符
    console.warn('GLTF导出功能尚未实现', {
      includeAssets: options.includeAssets,
      compressAssets: options.compressAssets,
      optimizeForWeb: options.optimizeForWeb,
      sceneDataType: typeof sceneData
    });

    // 基于选项创建不同的GLTF结构
    const gltfData = {
      asset: {
        version: '2.0',
        generator: 'DL Engine Scene Exporter',
        copyright: options.optimizeForWeb ? 'Optimized for Web' : 'Standard Export'
      },
      scene: 0,
      scenes: [
        {
          name: sceneData?.name || 'Scene',
          nodes: []
        }
      ],
      nodes: [],
      meshes: [],
      materials: [],
      textures: options.includeAssets ? [] : undefined,
      images: options.includeAssets ? [] : undefined
    };

    // 移除undefined属性
    Object.keys(gltfData).forEach(key => {
      if (gltfData[key as keyof typeof gltfData] === undefined) {
        delete gltfData[key as keyof typeof gltfData];
      }
    });

    return gltfData;
  }

  // ==================== 场景模板管理 ====================

  /**
   * 获取所有场景模板
   */
  public async getSceneTemplates(): Promise<SceneTemplate[]> {
    try {
      // 如果缓存为空，从API加载
      if (this.sceneTemplates.size === 0) {
        await this.loadSceneTemplates();
      }

      return Array.from(this.sceneTemplates.values());
    } catch (error) {
      console.error('获取场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 从API加载场景模板
   */
  private async loadSceneTemplates(): Promise<void> {
    try {
      const response = await axios.get('/api/scene-templates');
      const templates = response.data;

      // 清空缓存并重新加载
      this.sceneTemplates.clear();

      templates.forEach((template: SceneTemplate) => {
        this.sceneTemplates.set(template.id, template);
      });
    } catch (error) {
      console.error('加载场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定模板
   * @param templateId 模板ID
   */
  public async getSceneTemplate(templateId: string): Promise<SceneTemplate | null> {
    try {
      // 先检查缓存
      const cached = this.sceneTemplates.get(templateId);
      if (cached) {
        return cached;
      }

      // 从API获取
      const response = await axios.get(`/api/scene-templates/${templateId}`);
      const template = response.data;

      // 更新缓存
      this.sceneTemplates.set(templateId, template);

      return template;
    } catch (error) {
      console.error('获取场景模板失败:', error);
      return null;
    }
  }

  /**
   * 创建自定义模板
   * @param name 模板名称
   * @param description 模板描述
   * @param category 模板分类
   */
  public async createSceneTemplate(name: string, description: string, category: string = 'custom'): Promise<SceneTemplate> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }

    try {
      // 获取当前场景数据
      const sceneData = await EngineService.saveScene();

      // 生成缩略图（这里是占位符）
      const thumbnail = await this.generateSceneThumbnail();

      // 创建模板
      const templateData = {
        name,
        description,
        category,
        data: sceneData,
        thumbnail,
        isBuiltIn: false
      };

      const response = await axios.post('/api/scene-templates', templateData);
      const template = response.data;

      // 更新缓存
      this.sceneTemplates.set(template.id, template);

      console.log('场景模板创建成功');
      return template;
    } catch (error) {
      console.error('创建场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 删除场景模板
   * @param templateId 模板ID
   */
  public async deleteSceneTemplate(templateId: string): Promise<void> {
    try {
      const template = this.sceneTemplates.get(templateId);
      if (template && template.isBuiltIn) {
        throw new Error('无法删除内置模板');
      }

      // 从API删除
      await axios.delete(`/api/scene-templates/${templateId}`);

      // 从缓存中移除
      this.sceneTemplates.delete(templateId);

      console.log('场景模板删除成功');
    } catch (error) {
      console.error('删除场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 生成场景缩略图
   */
  private async generateSceneThumbnail(): Promise<string> {
    try {
      // 这里应该实现场景截图功能
      // 由于复杂性，这里返回一个占位符
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    } catch (error) {
      console.error('生成缩略图失败:', error);
      return '';
    }
  }

  // ==================== 场景预览功能 ====================

  /**
   * 生成场景预览图
   * @param width 宽度
   * @param height 高度
   */
  public async generateScenePreview(width: number = 256, height: number = 256): Promise<string> {
    try {
      // 这里应该调用引擎的截图功能
      // 由于没有具体的API，这里是占位符
      console.log(`生成场景预览图: ${width}x${height}`);

      // 返回占位符图片
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    } catch (error) {
      console.error('生成场景预览图失败:', error);
      throw error;
    }
  }

  /**
   * 更新场景信息
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param updates 更新数据
   */
  public async updateSceneInfo(projectId: string, sceneId: string, updates: Partial<SceneInfo>): Promise<void> {
    try {
      // 更新场景信息
      await axios.patch(`/api/projects/${projectId}/scenes/${sceneId}/info`, updates);

      // 更新缓存
      const sceneInfo = this.sceneInfoCache.get(sceneId);
      if (sceneInfo) {
        Object.assign(sceneInfo, updates);
        sceneInfo.updatedAt = new Date();
      }

      console.log('场景信息更新成功');
    } catch (error) {
      console.error('更新场景信息失败:', error);
      throw error;
    }
  }

  // ==================== 实用工具方法 ====================

  /**
   * 序列化场景数据（缺失的方法实现）
   */
  private async serializeScene(): Promise<any> {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        throw new Error('没有活动场景');
      }

      // 这里应该实现场景序列化逻辑
      // 由于没有具体的API，这里返回基本数据
      return {
        version: '1.0',
        timestamp: new Date().toISOString(),
        sceneGraph: this.sceneGraph,
        entities: scene.getEntities ? scene.getEntities() : [],
        metadata: {
          projectId: this.currentProjectId,
          sceneId: this.currentSceneId
        }
      };
    } catch (error) {
      console.error('序列化场景失败:', error);
      throw error;
    }
  }

  /**
   * 从数据加载场景（缺失的方法实现）
   * @param sceneData 场景数据
   */
  private async loadSceneFromData(sceneData: any): Promise<void> {
    try {
      // 这里应该实现从数据加载场景的逻辑
      // 由于没有具体的API，这里只是占位符
      console.log('从数据加载场景:', sceneData);

      // 模拟加载过程
      if (sceneData.entities) {
        // 加载实体
        console.log(`加载 ${sceneData.entities.length} 个实体`);
      }

      if (sceneData.sceneGraph) {
        // 恢复场景图
        this.sceneGraph = sceneData.sceneGraph;
      }
    } catch (error) {
      console.error('从数据加载场景失败:', error);
      throw error;
    }
  }

  /**
   * 获取场景统计信息
   */
  public getSceneStatistics(): any {
    const scene = EngineService.getActiveScene();
    const entities = scene?.getEntities ? scene.getEntities() : [];

    return {
      entityCount: entities.length,
      nodeCount: this.sceneGraph ? this.countNodes(this.sceneGraph) : 0,
      selectedNodeCount: this.selectedNodes.size,
      undoStackSize: this.undoStack.length,
      redoStackSize: this.redoStack.length,
      isDirty: this.isDirty,
      lastValidationTime: this.lastValidationTime,
      validationErrorCount: this.validationErrors.length
    };
  }

  /**
   * 递归计算节点数量
   * @param node 节点
   */
  private countNodes(node: SceneGraphNode): number {
    let count = 1; // 当前节点
    for (const child of node.children) {
      count += this.countNodes(child);
    }
    return count;
  }

  /**
   * 销毁场景服务
   */
  public dispose(): void {
    // 清除自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    // 移除事件监听
    EngineService.removeEventListener(EngineEventType.SCENE_LOADED, this.handleSceneLoaded);
    EngineService.removeEventListener(EngineEventType.SCENE_UNLOADED, this.handleSceneUnloaded);
    EngineService.removeEventListener(EngineEventType.OBJECT_ADDED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.OBJECT_REMOVED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.OBJECT_CHANGED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.COMPONENT_ADDED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.COMPONENT_REMOVED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.COMPONENT_CHANGED, this.handleObjectChanged);

    // 清除所有事件监听器
    this.removeAllListeners();
  }
}

export default SceneService.getInstance();
